/**
 * Test Helpers
 * Common utilities for testing
 */

class TestHelpers {
    static async waitForElement(element, timeout = 10000) {
        await element.waitForDisplayed({ timeout });
    }

    static async takeScreenshot(name) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        await browser.saveScreenshot(`./screenshots/${name}_${timestamp}.png`);
    }

    static async scrollDown() {
        const { width, height } = await browser.getWindowSize();
        await browser.touchAction([
            { action: 'press', x: width / 2, y: height * 0.7 },
            { action: 'wait', ms: 1000 },
            { action: 'moveTo', x: width / 2, y: height * 0.3 },
            { action: 'release' }
        ]);
        await browser.pause(1000);
    }

    static async scrollUp() {
        const { width, height } = await browser.getWindowSize();
        await browser.touchAction([
            { action: 'press', x: width / 2, y: height * 0.3 },
            { action: 'wait', ms: 1000 },
            { action: 'moveTo', x: width / 2, y: height * 0.7 },
            { action: 'release' }
        ]);
        await browser.pause(1000);
    }

    static generateTestData() {
        return {
            text: `Test_${Date.now()}`,
            email: `test${Date.now()}@example.com`,
            number: Math.floor(Math.random() * 1000).toString(),
            date: new Date().toISOString().split('T')[0]
        };
    }
}

module.exports = TestHelpers;
